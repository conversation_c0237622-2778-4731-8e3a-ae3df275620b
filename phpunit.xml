<?xml version="1.0" encoding="UTF-8"?>
<phpunit
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.2/phpunit.xsd"
        bootstrap="vendor/autoload.php"
        colors="true"
        displayDetailsOnTestsThatTriggerDeprecations="true"
        displayDetailsOnTestsThatTriggerErrors="true"
        displayDetailsOnTestsThatTriggerNotices="true"
        displayDetailsOnTestsThatTriggerWarnings="true"
        cacheDirectory=".cache/phpunit"
>
  <testsuites>
    <testsuite name="Unit">
      <directory suffix="Test.php">./tests</directory>
    </testsuite>
    <testsuite name="rector">
      <directory>utils/rector/tests</directory>
    </testsuite>
  </testsuites>
  <coverage/>
  <source>
    <include>
      <directory suffix=".php">./app</directory>
    </include>
  </source>
  <php>
    <env name="ENVIRONMENT" value="testing" />
    <env name="BASE_URI" value="" />
    <env name="CACHE" value="null" />
    <env name="DB_HOST" value="localhost" />
    <env name="DB_PORT" value="3306" />
    <env name="DB_USERNAME" value="root" />
    <env name="DB_PASSWORD" value="" />
    <env name="DB_DATABASE" value="tempest_test" />
  </php>
</phpunit>
