<?php

declare(strict_types=1);

namespace App\Models;

use Tempest\Database\IsDatabaseModel;
use Tempest\Router\Bindable;

class Author implements Bindable
{
    use IsDatabaseModel;

    public function __construct(
        public string $name,
        public ?AuthorType $type = AuthorType::A,

        /** @var \App\Models\Book[] */
        public array $books = [],
        public ?Publisher $publisher = null,
    ) {
        //
    }

    public static function resolve(string $input): self
    {
        return self::get($input);
    }

    public function toArray()
    {
        return [
            'id' => $this->id->id,
            'name' => $this->name,
            'email' => $this->email,
        ];
    }
}
