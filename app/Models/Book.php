<?php

declare(strict_types=1);

namespace App\Models;

use Tempest\Database\IsDatabaseModel;
use Tempest\Router\Bindable;
use Tempest\Validation\Rules\Length;

final class Book implements Bindable
{
    use IsDatabaseModel;

    #[Length(min: 3, max: 50)]
    public string $title;

    public Author $author;

    public static function resolve(string $input): self
    {
        return self::find(id: $input)
            ->with('author')
            ->first();
    }

    public function toArray()
    {
        return [
            'id' => $this->id->id,
            'title' => $this->title,
            'author' => $this->author->toArray(),
        ];
    }
}
