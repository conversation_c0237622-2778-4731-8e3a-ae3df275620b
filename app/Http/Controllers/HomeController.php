<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\CreateBookRequest;
use App\Models\Author;
use App\Models\Book;
use App\Models\Publisher;
use Tempest\Http\Responses\Redirect;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\View\View;

use function Tempest\view;

final readonly class HomeController
{
    #[Get('/')]
    public function __invoke(): View
    {
        $books = Book::select()
            ->with('authors.publishers')
            ->all();

        return view('Views/Books/index.view.php', title: 'Book Listing', books: $books);
    }

    #[Get('/books/{bookId}')]
    public function show(int $bookId): View
    {
        $book = Book::find(id: $bookId)
            ->with('author.publisher')
            ->first();

        return view('Views/Books/show.view.php', title: 'Details', book: $book);
    }

    #[Get('/books/create')]
    public function create(): View
    {
        $publishers = Publisher::select()->all();
        $authors = Author::select()->all();

        return view('Views/Books/create.view.php', title: 'Create Book', authors: $authors, publishers: $publishers);
    }

    #[Post('/books')]
    public function store(CreateBookRequest $request)
    {
        $author = Author::find(id: $request->authorId)->first();

        Book::create(title: $request->title, author: $author);

        return new Redirect('/');
    }
}
