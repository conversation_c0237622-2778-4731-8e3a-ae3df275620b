<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;

return RectorConfig::configure()
    ->withPaths([
        __DIR__ . '/app',
        __DIR__ . '/public',
        __DIR__ . '/tests',
    ])
    ->withRules([
        \Utils\Rector\Rector\RenameIdToPrimaryKeyRector::class,
    ])
    ->withImportNames(importDocBlockNames: false, importShortClasses: false, removeUnusedImports: true)
     ->withPhpSets(php84: true)
    ->withTypeCoverageLevel(0)
    ->withDeadCodeLevel(0)
    ->withCodeQualityLevel(0);
