<?php

declare(strict_types=1);

namespace Utils\Rector\Rector;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON>\Rector\AbstractRector;
use Symplify\RuleDocGenerator\ValueObject\CodeSample\CodeSample;
use Symplify\RuleDocGenerator\ValueObject\RuleDefinition;
use Tempest\Database\PrimaryKey;

/**
 * @see \Rector\Tests\TypeDeclaration\Rector\RenameIdToPrimaryKeyRector\RenameIdToPrimaryKeyRectorTest
 */
final class RenameIdToPrimaryKeyRector extends AbstractRector
{
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        return [\PhpParser\Node\Stmt\Property::class];
    }

    /**
     * @param \PhpParser\Node\Stmt\Property $node
     */
    public function refactor(Node $node): ?Node
    {
        // Check if property has a type hint
        if ($node->type === null) {
            return null;
        }

        // Check if the type hint is a Name node (simple class name or fully qualified)
        if (!$node->type instanceof \PhpParser\Node\Name) {
            return null;
        }

        // Get the class name (last part of the namespace)
        $typeName = $node->type->toString();
        $parts = explode('\\', $typeName);
        $className = end($parts);

        // Check if the class name is exactly "Id"
        if ($className !== 'Id') {
            return null;
        }

        if (! class_exists(PrimaryKey::class)) {
            return null;
        }

        // Replace "Id" with "PrimaryKey"
        $node->type = new \PhpParser\Node\Name\FullyQualified(PrimaryKey::class);

        return $node;
    }
}
