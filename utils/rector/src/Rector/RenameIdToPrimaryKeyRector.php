<?php

declare(strict_types=1);

namespace Utils\Rector\Rector;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON>\Rector\AbstractRector;

/**
 * @see \Rector\Tests\TypeDeclaration\Rector\RenameIdToPrimaryKeyRector\RenameIdToPrimaryKeyRectorTest
 */
final class RenameIdToPrimaryKeyRector extends AbstractRector
{
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        // @todo select node type
        return [\PhpParser\Node\Stmt\PropertyProperty::class];
    }

    /**
     * @param \PhpParser\Node\Stmt\Class_ $node
     */
    public function refactor(Node $node): ?Node
    {
        lw($node->getAttributes());

        return $node;
    }
}
