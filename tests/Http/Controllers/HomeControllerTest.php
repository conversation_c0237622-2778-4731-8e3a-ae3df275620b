<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Migrations\CreateAuthorTable;
use App\Migrations\CreateBookTable;
use App\Migrations\CreateChapterTable;
use App\Migrations\CreateIsbnTable;
use App\Migrations\CreatePublishersTable;
use App\Models\Author;
use App\Models\AuthorType;
use App\Models\Book;
use App\Models\Publisher;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class HomeControllerTest extends IntegrationTestCase
{
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->migrate(
            CreateMigrationsTable::class,
            CreatePublishersTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class,
            CreateChapterTable::class,
            CreateIsbnTable::class,
        );
    }

    #[Test]
    public function get_book_listing(): void
    {
        $this->http->get('/')->assertOk();
    }

    #[Test]
    public function show_book_details(): void
    {
        $this->http->get('/books/1')->assertOk();
    }

    #[Test]
    public function create_new_book_details(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Greece Publishers', description: 'The Greece Publishers Guild')
            ->execute();

        $authorId = query(Author::class)
            ->insert(name: 'Homer', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $response = $this->http
            ->post('/books', [
                'title' => 'Homer',
                'authorId' => $authorId->id,
            ]);

        $response->assertRedirect(to: '/');

        self::assertCount(1, Book::select()->all());
    }
}
